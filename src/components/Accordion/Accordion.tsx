import React, { ReactNode, SyntheticEvent, useCallback, useRef } from "react";

import { OpenCloseIcon } from "../../components/Icon/index.ts";
import { Text } from "../../components/Text/index.ts";
import { Box, Inline, Stack } from "../../fermions/index.ts";
import {
  composeComponentClassNames,
  returnStringIfTrue
} from "../../helpers/componentHelpers.ts";
import "./Accordion.scss";

export interface CustomAccordionTriggerProps {
  onClick?: (e: SyntheticEvent<HTMLElement>) => void;
  isOpen?: boolean;
}

export type CustomAccordionTrigger = (
  props: CustomAccordionTriggerProps
) => ReactNode;

export interface AccordionProps {
  trigger: string | CustomAccordionTrigger;
  isOpen: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  className?: string;
  contentClassName?: string;
  contentOverflow?: "auto" | "visible";
  style?: React.CSSProperties;
}

export const Accordion = ({
  trigger,
  isOpen = false,
  onOpenChange,
  className = "",
  contentClassName = "",
  contentOverflow = "auto",
  style = {},
  children
}: React.PropsWithChildren<AccordionProps>) => {
  const triggerRef = useRef<HTMLDivElement>(null);
  const handleClick = useCallback(
    (e: SyntheticEvent<HTMLElement>) => {
      e.preventDefault();
      e.stopPropagation();
      onOpenChange?.(!isOpen);
    },
    [isOpen, onOpenChange]
  );
  return (
    <Stack
      classNames={[composeComponentClassNames("accordion", {}), className]}
      alignment="left"
      style={style}
    >
      <Box ref={triggerRef} width="100" className="accordion__trigger">
        {typeof trigger === "string" ? (
          <button
            className="accordion__trigger__default"
            onClick={handleClick}
            onMouseDown={e => e.preventDefault()}
          >
            <Inline className="accordion__trigger__default" alignment="center">
              <OpenCloseIcon isOpen={isOpen} withTransition />
              <Text>{trigger}</Text>
            </Inline>
          </button>
        ) : (
          trigger({ onClick: handleClick, isOpen })
        )}
      </Box>
      <Box
        classNames={[
          "accordion__content",
          returnStringIfTrue(isOpen, "accordion__content--open"),
          returnStringIfTrue(
            contentOverflow === "visible",
            "accordion__content--overflow-visible"
          ),
          contentClassName
        ]}
      >
        {children}
      </Box>
    </Stack>
  );
};
