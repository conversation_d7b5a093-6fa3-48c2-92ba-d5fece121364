// QuestionTablePreview.tsx
import React, { useState } from "react";

import { Stack, TabGroup } from "@oneteam/onetheme";

import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";

import { Question } from "@src/types/Question";
import { TableQuestionProperties } from "@src/types/QuestionProperties";

export const QuestionTablePreview = ({
  question,
  parentQuestion
}: {
  question: Question;
  parentQuestion?: Question;
}) => {
  const [tablePreviewPanelTab, setTablePreviewPanelTab] =
    useState<string>("all");

  return (
    <Stack
      className="question-table-preview__container"
      style={{
        flex: "1 1 auto",
        minHeight: 0,
        height: "100%",
        display: "flex",
        flexDirection: "column"
      }}
    >
      {/* Header (pinned) */}
      <div className="question-table-preview__header">
        <TabGroup
          options={[
            { value: "all", label: "All" },
            { value: "graph", label: "Graph" },
            { value: "table", label: "Table" }
          ]}
          value={tablePreviewPanelTab}
          handleChange={setTablePreviewPanelTab}
        />
      </div>

      {/* Scrollable answers */}
      <div className="question-table-preview__answers">
        {tablePreviewPanelTab === "all" && (
          <>
            {(question?.properties as TableQuestionProperties)?.charts?.map(
              chart => (
                <QuestionAnswer
                  key={chart.id}
                  question={chart}
                  answer={undefined}
                  answerAccessor={""}
                  parentQuestion={question ?? undefined}
                />
              )
            )}
            <QuestionAnswer
              question={question}
              answer={undefined}
              answerAccessor={""}
              parentQuestion={parentQuestion ?? undefined}
            />
          </>
        )}
        {tablePreviewPanelTab === "graph" && (
          <>
            {(question?.properties as TableQuestionProperties)?.charts?.map(
              chart => (
                <QuestionAnswer
                  key={chart.id}
                  question={chart}
                  answer={undefined}
                  answerAccessor={""}
                  parentQuestion={question ?? undefined}
                />
              )
            )}
          </>
        )}
        {tablePreviewPanelTab === "table" && (
          <QuestionAnswer
            question={question}
            answer={undefined}
            answerAccessor={""}
            parentQuestion={parentQuestion ?? undefined}
          />
        )}
      </div>
    </Stack>
  );
};
