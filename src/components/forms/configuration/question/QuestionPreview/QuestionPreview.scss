.question-preview {
  margin-top: var(--spacing-075);
  background-color: var(--color-surface-primary);
  padding: var(--spacing-200);
  border-radius: var(--border-radius-rounded);

  display: flex;
  flex-direction: column;
  max-height: 50vh;
  overflow: hidden;

  // Override accordion default behavior to enable proper scrolling
  .accordion__content--open {
    // Override the default accordion behavior with a constrained height
    // This creates the necessary overflow for scrolling to work
    height: 35vh !important;
    max-height: 35vh !important;
    overflow: hidden !important;

    display: flex;
    flex-direction: column;
    visibility: visible;
    opacity: 1;
    flex: 1 1 auto;
    min-height: 0;
  }
}

.question-table-preview__container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;

  .question-table-preview__header {
    flex-shrink: 0;
    margin-bottom: var(--spacing-100);
  }

  .question-table-preview__answers {
    flex: 1 1 auto;
    min-height: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-150);
    padding-bottom: var(
      --spacing-200
    ); // Add bottom padding to ensure content is fully scrollable

    // Constrain child components to create scrollable overflow
    > * {
      flex-shrink: 0;
    }

    // Override chart container height to be more reasonable in preview
    .chart-preview-container {
      min-height: 200px;
      height: 200px;
      flex-shrink: 0;
    }

    // Constrain table height in preview mode but allow it to be fully visible
    .handsontable {
      max-height: none; // Remove height constraint to show full table
      flex-shrink: 0;
    }

    // Ensure table container doesn't get cut off
    .question-table-answer {
      flex-shrink: 0;
    }
  }
}
