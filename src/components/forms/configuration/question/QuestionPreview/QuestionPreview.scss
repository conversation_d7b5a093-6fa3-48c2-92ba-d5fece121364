.question-preview {
  margin-top: var(--spacing-075);
  background-color: var(--color-surface-primary);
  padding: var(--spacing-200);
  border-radius: var(--border-radius-rounded);

  display: flex;
  flex-direction: column;
  max-height: 50vh;
  overflow: hidden;

  .accordion__content--open {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    visibility: visible;
    opacity: 1;
    flex: 1 1 auto;
    min-height: 0;
    height: 100%;
  }
}

.question-table-preview__container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;

  .question-table-preview__header {
    flex-shrink: 0;
  }

  .question-table-preview__answers {
    flex: 1 1 auto;
    min-height: 0;
    overflow-y: auto;
  }
}
